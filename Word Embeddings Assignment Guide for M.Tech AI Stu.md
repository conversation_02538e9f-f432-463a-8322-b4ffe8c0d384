Assignment 1: Word Embeddings \- Theory and Analysis (Conceptual) 

Key Topics to Cover: 

One-hot Encoding vs Dense Word Embeddings 

One-hot encoding creates sparse vectors where each word is represented by a vector with vocabulary size dimensions, with only one position set to 1 and all others as 0\. This approach suffers from the curse of dimensionality and lacks semantic relationships between words.\[1\] \[2\] 

Dense embeddings are compact, continuous-valued vectors (typically 50-300 dimensions) where most values contain meaningful information. They capture semantic and syntactic relationships, allowing similar words to have similar vector representations.\[3\] \[1\] 

Word2Vec Architectures Comparison 

CBOW (Continuous Bag of Words): Predicts target words from context words. It's faster to train, works better with frequent words, and uses smaller context windows (2-5 words). CBOW performs well for syntactic relationships and similarity tasks.\[4\] \[5\] 

Skip-Gram: Predicts context words from target words. It's slower to train but better with rare words, handles larger context windows (5-20 words), and excels at semantic relationships and word diversity. Skip-gram typically produces better quality embeddings for rare words.\[5\] \[4\] 

GloVe vs FastText Comparison 

GloVe combines global statistical information with local context windows, using word co occurrence matrices and pointwise mutual information. It performs well on word analogy tasks and captures both syntactic and semantic relationships.\[6\] \[7\] 

FastText extends Word2Vec by incorporating subword information through character n grams. This allows it to handle out-of-vocabulary words and morphologically rich languages better. FastText often outperforms other methods in various NLP tasks, achieving 97.2% accuracy compared to 95.8% for GloVe and 92.5% for Word2Vec in some studies.\[8\] \[9\]

Semantic Similarity in Embedding Spaces   
Words with similar meanings occupy nearby positions in embedding space\[10\] \[11\] 

Cosine similarity is the primary metric for measuring semantic relationships, calculated as the dot product of normalized vectors\[12\] \[10\] 

Vector arithmetic enables analogical reasoning: "king \- man \+ woman ≈ queen"\[13\] \[14\] Role of Dimensionality 

Common dimensions range from 50-300 for general purposes, with 100-300 being most effective\[15\] \[16\] \[3\] 

Higher dimensions capture more nuanced relationships but increase computational costs and risk overfitting\[3\] \[15\] 

The optimal dimension depends on dataset size, task complexity, and computational constraints\[16\] 

Assignment 2: Embedding Space Exploration (Conceptual) 

Tasks to Complete: 

Cosine Similarity Computations 

Calculate cosine similarity between word pairs using the formula: 

`cosine_similarity = (A · B) / (||A|| × ||B||)` 

\[10\] \[11\]   
Where A and B are word vectors, and ||·|| represents the Euclidean norm . 

Vector Arithmetic Analysis 

Demonstrate analogical relationships: 

Gender: king \- man \+ woman ≈ queen 

Geography: Paris \- France \+ Italy ≈ Rome 

Tense: walked \- walk \+ run ≈ ran\[14\] \[17\] \[13\] 

Note: The famous "king \- man \+ woman \= queen" example requires some caveats \- 

\[18\]   
implementations often exclude input words from results to avoid the trivial solution. Visualization with PCA/t-SNE 

PCA: Linear dimensionality reduction that preserves global structure and maximizes variance\[19\] \[20\] 

t-SNE: Non-linear technique that preserves local relationships and is excellent for visualizing clusters\[21\] \[20\] \[19\] 

t-SNE is preferred for embedding visualization as it better reveals semantic clusters and word relationships\[22\] \[21\]  
Assignment 3: Build a Word2Vec Model (Programming) 

Implementation Steps: 

Text Preprocessing 

Tokenization: Split text into individual words 

Vocabulary creation: Build word-to-index mappings 

Handle special tokens and rare words 

Create training pairs based on context windows\[23\] \[24\] 

Negative Sampling Implementation 

Select 5-15 negative samples per positive example\[25\] \[24\] \[23\] 

Use frequency-based sampling with the formula: P(wi) \= f(wi)^0.75 / Σf(wj)^0.75\[24\] 

Update only a small subset of weights (target word \+ negative samples) instead of entire vocabulary\[23\] \[24\] 

Training Process 

Implement either Skip-gram or CBOW architecture 

Use negative sampling for computational efficiency 

Apply gradient descent to optimize word embeddings 

Monitor training loss and convergence\[23\] 

Evaluation Methods 

Word similarity tasks using cosine similarity 

Analogical reasoning tests 

Intrinsic evaluation on standard datasets 

Visualization of learned embeddings\[23\] 

Assignment 4: Pre-trained Embeddings Application (Programming) 

Implementation Options: 

Loading Pre-trained Models 

GloVe: Download from Stanford's project website, available in 50d, 100d, 200d, 300d variants\[9\] \[7\] \[6\] 

FastText: Available for 157 languages, 300-dimensional vectors trained on Common Crawl and Wikipedia\[26\] \[9\] 

Sentiment Classification (IMDB Dataset) 

Use 25,000 movie reviews with binary sentiment labels\[27\] \[28\] \[29\] 

Compare performance with and without pre-trained embeddings  
Implement using embedding layer \+ Conv1D \+ Dense layers\[28\] \[29\] 

Expected accuracy improvements: embeddings can boost performance significantly over bag-of-words approaches\[27\] \[28\] 

Named Entity Recognition (CoNLL Dataset) 

Use CoNLL-2003 dataset with 4 entity types: PER, LOC, ORG, MISC\[30\] \[31\] \[32\] \[33\] Implement sequence tagging with LSTM/BiLSTM architecture 

Compare static embeddings vs no embeddings baseline 

Handle IOB2 tagging scheme for entity boundaries\[32\] \[30\] 

Performance Comparison Framework 

Baseline: No embeddings or one-hot encoding 

With embeddings: Pre-trained GloVe/FastText 

Metrics: Accuracy, F1-score, precision, recall 

Statistical significance testing of improvements\[8\] \[28\] 

Assignment 5: Contextualized vs Static Embeddings (Programming) 

Key Comparisons: 

Static Embeddings (Word2Vec/GloVe) 

Fixed representations regardless of context 

Single vector per word type 

Efficient for storage and computation 

Limited ability to handle polysemy\[34\] \[35\] \[36\] 

Contextualized Embeddings (BERT/ELMo) 

Dynamic representations based on context 

Same word can have different embeddings in different sentences 

Better handling of word sense disambiguation\[37\] \[35\] \[38\] \[34\] 

Significantly higher computational requirements 

Implementation with Hugging Face Transformers 

`from transformers import BertTokenizer, BertModel` 

`tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')` 

`model = BertModel.from_pretrained('bert-base-uncased')` 

Analysis Tasks 

Sentence Similarity: Compare performance on semantic textual similarity tasks  
Word Sense Disambiguation: Evaluate ability to distinguish different word meanings in context\[39\] \[38\] \[37\] 

Performance Metrics: Compare accuracy, computational efficiency, and interpretability\[35\] \[36\] 

Expected Findings 

Contextualized embeddings typically outperform static ones on downstream tasks BERT achieves state-of-the-art results on most NLP benchmarks   
Trade-off between performance gains and computational costs\[40\] \[35\] 

Technical Implementation Tips: 

1\. Environment Setup: Use Python with PyTorch/TensorFlow, NumPy, and scikit-learn 2\. Data Handling: Implement proper text preprocessing pipelines 

3\. Evaluation: Include both intrinsic (word similarity) and extrinsic (downstream task) evaluations 

4\. Visualization: Use matplotlib/seaborn for embedding space visualizations 5\. Reproducibility: Set random seeds and document hyperparameters 

Key Resources for Implementation: 

Datasets: IMDB reviews, CoNLL-2003 NER, word similarity benchmarks 

Pre-trained Models: GloVe vectors, FastText embeddings, BERT checkpoints from Hugging Face 

Libraries: Transformers, Gensim, NLTK, spaCy 

Evaluation Tools: Standard NLP evaluation metrics and benchmarks 

This comprehensive approach will help you understand both the theoretical foundations and practical applications of word embeddings, preparing you well for advanced NLP research and applications. 

⁂ 

1\. https://milvus.io/ai-quick-reference/why-are-embeddings-called-dense-representations 2\. https://www.ibm.com/think/topics/word-embeddings 

3\. https://milvus.io/ai-quick-reference/how-does-dimensionality-affect-embedding-performance 

4\. https://www.geeksforgeeks.org/nlp/word-embeddings-in-nlp-comparison-between-cbow-and-skip-gr am-models/ 

5\. https://www.educative.io/answers/cbow-vs-skip-gram 

6\. https://www.geeksforgeeks.org/nlp/pre-trained-word-embedding-using-glove-in-nlp-models/ 7\. https://nlp.stanford.edu/projects/glove/ 

8\. http://www.jatit.org/volumes/Vol100No2/5Vol100No2.pdf 

9\. https://fasttext.cc/docs/en/english-vectors.html  
10\. https://www.tigerdata.com/learn/understanding-cosine-similarity 

11\. https://learn.microsoft.com/en-us/azure/ai-foundry/openai/concepts/understand-embeddings 

12\. https://fastdatascience.com/natural-language-processing/semantic-similarity-with-sentence-embeddin gs/ 

13\. https://stackoverflow.blog/2023/11/09/an-intuitive-introduction-to-text-embeddings/ 14\. https://kawine.github.io/blog/nlp/2019/06/21/word-analogies.html 

15\. https://zilliz.com/ai-faq/what-is-the-impact-of-dimensionality-on-embedding-quality 16\. https://www.nature.com/articles/s41467-021-23795-5 

17\. https://www.technologyreview.com/2015/09/17/166211/king-man-woman-queen-the-marvelous-mathe matics-of-computational-linguistics/ 

18\. https://blog.esciencecenter.nl/king-man-woman-king-9a7fd2935a85 

19\. https://www.geeksforgeeks.org/machine-learning/difference-between-pca-vs-t-sne/ 

20\. https://apxml.com/courses/nlp-fundamentals/chapter-4-nlp-word-embeddings/visualizing-word-embe ddings 

21\. https://www.youtube.com/watch?v=MgayYUdI4is 

22\. https://towardsdatascience.com/visualizing-word-embedding-with-pca-and-t-sne-961a692509f5/ 23\. https://www.geeksforgeeks.org/nlp/negaitve-sampling-using-word2vec/ 

24\. http://mccormickml.com/2017/01/11/word2vec-tutorial-part-2-negative-sampling/ 25\. https://milvus.io/ai-quick-reference/what-are-negative-sampling-and-its-role-in-embedding-training 26\. https://fasttext.cc/docs/en/crawl-vectors.html 

27\. https://github.com/Karthik-Bhaskar/Sentiment-Analysis 

28\. https://thedatafrog.com/en/articles/word-embedding-sentiment-analysis/ 

29\. https://pub.aimind.so/classifying-imdb-sentiment-with-keras-dropout-and-conv1d-fecaf69f52b5 30\. https://huggingface.co/datasets/eriktks/conll2002 

31\. https://huggingface.co/datasets/eriktks/conll2003 

32\. https://www.tensorflow.org/datasets/catalog/conll2003 

33\. https://aclanthology.org/W03-0419.pdf 

34\. https://jalammar.github.io/illustrated-bert/ 

35\. https://ai.stanford.edu/blog/contextual/ 

36\. https://www.prompts.ai/en/blog-details/ultimate-guide-to-static-and-contextual-embeddings 37\. https://aclanthology.org/2020.lrec-1.676/ 

38\. https://arxiv.org/abs/1909.10430 

39\. https://www.inf.uni-hamburg.de/en/inst/ab/lt/publications/2020-logachevaetal-lrec20-158wsd.pdf 40\. https://www.scaler.com/topics/nlp/huggingface-transformers/