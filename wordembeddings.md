**Instruction : Out of total 5 assignments, you are free to pick any 3 assignments including at least one  “Conceptual Assignments”.** 

**Conceptual Assignments (Write Handwritten Notes. Scan and upload PDF.) 1: Word Embeddings – Theory and Analysis** 

• **Objective:** Explain the concept of word embeddings. 

• **Tasks:** 

o Compare one-hot encoding with dense word embeddings. 

o Explain the differences between Word2Vec (CBOW vs. Skip-Gram), GloVe, and FastText. o Discuss semantic similarity in embedding spaces with examples. 

o Analyze the role of dimensionality in embedding representations. 

**2: Embedding Space Exploration** 

• **Objective:** Explore vector relationships. 

• **Tasks:** 

o Given a list of word embeddings, perform: 

▪ Cosine similarity computations between words. 

▪ Vector arithmetic (e.g., king \- man \+ woman ≈ queen). 

o Visualize embeddings using PCA or t-SNE. (**Optional if you can only**) 

o Interpret the visualized clusters (**Optional if you can only**). 

**Programming Assignments (Code) :** 

**3: Build a Word2Vec Model** 

• Objective: Implement Skip-Gram or CBOW using a small corpus. 

• Tools: Python, NumPy, or PyTorch/TensorFlow. 

• Tasks: 

o Preprocess text (tokenization, vocabulary indexing). 

o Implement training using negative sampling. 

o Evaluate with similarity tasks or analogies. 

**4: Use Pre-trained Embeddings**  
• Objective: Apply pre-trained embeddings in downstream tasks. 

• Tasks: 

o Load GloVe or FastText embeddings. 

o Use embeddings as input for(***You can choose any one of below task.***): ▪ Sentiment classification (IMDB/Twitter dataset). 

▪ Named Entity Recognition (CoNLL dataset). 

o Compare performance with/without embeddings. 

**5: Contextualized vs Static Embeddings** 

• Objective: Compare static (Word2Vec/GloVe) and contextual embeddings (BERT/ELMo). • Tasks: 

o Use Hugging Face Transformers to extract contextual embeddings. 

o Analyze sentence similarity or word sense disambiguation. 

o Report differences in performance/interpretability.